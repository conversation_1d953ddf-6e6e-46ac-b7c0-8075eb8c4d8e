import React, { useState, useCallback, useEffect } from 'react';
import { ScrollView } from 'react-native';
import { Box } from '@indie-points/ui-box';
import { VStack } from '@indie-points/ui-vstack';
import { HStack } from '@indie-points/ui-hstack';
import { Heading } from '@indie-points/ui-heading';
import { Text } from '@indie-points/ui-text';
import { Spinner } from '@indie-points/ui-spinner';
import FontAwesome from '@expo/vector-icons/FontAwesome';
import { useAuth } from '@indie-points/contexts';
import { BusinessService, BusinessProfile } from '../../services';
import QRCode from 'react-native-qrcode-svg';
import { GradientBar } from '@indie-points/auth';

export default function Points() {
  const { user } = useAuth();
  const [businessProfile, setBusinessProfile] =
    useState<BusinessProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [qrWidth, setQrWidth] = useState(0);

  // Fetch business profile
  useEffect(() => {
    const fetchBusinessProfile = async () => {
      if (!user?.id) {
        setLoading(false);
        return;
      }

      setLoading(true);
      setError(null);

      const result = await BusinessService.getBusinessProfile(user.id);

      if (result.error) {
        setError(result.error);
        setBusinessProfile(null);
      } else if (!result.data) {
        setError(
          'No business profile found. Please complete your business setup in Settings.'
        );
        setBusinessProfile(null);
      } else {
        setBusinessProfile(result.data);
        setError(null);
      }

      setLoading(false);
    };

    fetchBusinessProfile();
  }, [user?.id]);

  // Helper to generate business QR code payload
  const generateBusinessQrPayload = useCallback(() => {
    if (!businessProfile) return '';

    const now = Date.now();
    return JSON.stringify({
      businessId: businessProfile.id,
      businessName: businessProfile.businessName,
      token: `${businessProfile.id}-${now}`, // Simple token generation
    });
  }, [businessProfile]);

  return (
    <Box className='flex-1 bg-background-0'>
      <ScrollView className='flex-1' showsVerticalScrollIndicator={false}>
        {/* Header Section */}
        <VStack space='lg' className='px-6 pt-6 pb-6'>
          {/* Title */}
          <Heading size='3xl' className='text-typography-900 font-bold'>
            Points
          </Heading>

          {/* Colored divider line */}
          <GradientBar />
        </VStack>

        <Box className='px-6 pb-8'>
          {loading ? (
            // Loading state
            <VStack space='lg' className='items-center py-8'>
              <Spinner size='large' />
              <Text size='md' className='text-typography-600'>
                Loading your business QR code...
              </Text>
            </VStack>
          ) : error ? (
            // Error state
            <VStack space='lg' className='items-center py-8'>
              <FontAwesome
                name='exclamation-triangle'
                size={48}
                color='#ef4444'
              />
              <Text
                size='lg'
                className='text-error-500 font-semibold text-center'
              >
                Unable to load QR code
              </Text>
              <Text size='md' className='text-typography-600 text-center'>
                {error}
              </Text>
            </VStack>
          ) : (
            <VStack space='xl'>
              {/* Business QR code section */}
              <VStack space='lg' className='items-center'>
                <Heading
                  size='xl'
                  className='text-typography-900 font-semibold'
                >
                  Your business QR code
                </Heading>
                <Text size='md' className='text-typography-600 text-center'>
                  Display this code for customers to scan and earn points
                </Text>

                {/* QR Code */}
                <Box
                  className='w-full max-w-md aspect-square bg-white rounded-2xl border-4 p-4 border-typography-900 items-center justify-center shadow-lg'
                  onLayout={event => {
                    const width = event.nativeEvent.layout.width;
                    setQrWidth(width);
                  }}
                >
                  <VStack space='md' className='items-center w-full h-full'>
                    {businessProfile && qrWidth > 0 ? (
                      <QRCode
                        value={generateBusinessQrPayload()}
                        size={Math.max(qrWidth - 8 - 32, 0)}
                        logo={require('../../assets/images/icon.png')}
                        logoSize={Math.max((qrWidth - 8 - 32) * 0.2, 32)}
                        logoBackgroundColor='transparent'
                      />
                    ) : (
                      <FontAwesome name='qrcode' size={120} color='#000' />
                    )}
                  </VStack>
                </Box>
              </VStack>

              {/* How to Use Your QR Code Section */}
              <VStack space='lg'>
                <Heading
                  size='xl'
                  className='text-typography-900 font-semibold'
                >
                  How customers earn points
                </Heading>

                {/* Step 1 */}
                <HStack space='md' className='items-start'>
                  <Box className='w-8 h-8 bg-primary-500 rounded-full items-center justify-center border-2 border-black'>
                    <Text size='md' className='text-white font-bold'>
                      1
                    </Text>
                  </Box>
                  <VStack className='flex-1'>
                    <Text
                      size='md'
                      className='text-typography-900 font-semibold'
                    >
                      Display your QR code
                    </Text>
                    <Text size='sm' className='text-typography-600'>
                      Show this QR code prominently in your business for
                      customers to see
                    </Text>
                  </VStack>
                </HStack>

                {/* Step 2 */}
                <HStack space='md' className='items-start'>
                  <Box className='w-8 h-8 bg-secondary-500 rounded-full items-center justify-center border-2 border-black'>
                    <Text size='md' className='text-white font-bold'>
                      2
                    </Text>
                  </Box>
                  <VStack className='flex-1'>
                    <Text
                      size='md'
                      className='text-typography-900 font-semibold'
                    >
                      Customers scan your code
                    </Text>
                    <Text size='sm' className='text-typography-600'>
                      Customers use the Indie Points app to scan your QR code
                      when they visit
                    </Text>
                  </VStack>
                </HStack>

                {/* Step 3 */}
                <HStack space='md' className='items-start'>
                  <Box className='w-8 h-8 bg-error-500 rounded-full items-center justify-center border-2 border-black'>
                    <Text size='md' className='text-white font-bold'>
                      3
                    </Text>
                  </Box>
                  <VStack className='flex-1'>
                    <Text
                      size='md'
                      className='text-typography-900 font-semibold'
                    >
                      Points awarded automatically
                    </Text>
                    <Text size='sm' className='text-typography-600'>
                      Customers earn 1 point for visiting, plus points for
                      purchases you process
                    </Text>
                  </VStack>
                </HStack>
              </VStack>
            </VStack>
          )}
        </Box>
      </ScrollView>
    </Box>
  );
}
