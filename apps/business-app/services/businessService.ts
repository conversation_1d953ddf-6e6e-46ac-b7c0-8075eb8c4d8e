import { supabase } from '@indie-points/lib';
import {
  BusinessProfile,
  BusinessPointsSummary,
  BusinessCustomerSummary,
  BusinessTransaction,
  PurchaseTransactionRequest,
  ServiceResponse,
} from './types';

/**
 * Business service for handling business profile and operations
 */
export class BusinessService {
  /**
   * Get business profile for a user
   * @param userId - The user's UUID
   * @returns Promise with business profile data or error
   */
  static async getBusinessProfile(
    userId: string
  ): Promise<ServiceResponse<BusinessProfile>> {
    try {
      const { data, error } = await supabase
        .from('business_profiles')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          // No rows returned - business profile doesn't exist
          return {
            data: null,
            error: null,
          };
        }
        console.error('Error fetching business profile:', error);
        return {
          data: null,
          error: error.message || 'Failed to fetch business profile',
        };
      }

      return {
        data: {
          id: data.id,
          userId: data.user_id,
          businessName: data.business_name,
          businessAddress: data.business_address,
          businessType: data.business_type,
          pointValuePercentage: parseFloat(data.point_value_percentage || '0'),
          minimumSpendForRedemption: parseFloat(
            data.minimum_spend_for_redemption || '0'
          ),
          onboardingCompleted: data.onboarding_completed || false,
          onboardingStep: data.onboarding_step || 0,
          createdAt: data.created_at,
          updatedAt: data.updated_at,
        },
        error: null,
      };
    } catch (error) {
      console.error('Unexpected error in getBusinessProfile:', error);
      return {
        data: null,
        error:
          error instanceof Error
            ? error.message
            : 'An unexpected error occurred',
      };
    }
  }

  /**
   * Upsert business profile (create if doesn't exist, update if it does)
   * @param userId - The user's UUID
   * @param profileData - The business profile data to upsert
   * @returns Promise with upserted business profile data or error
   */
  static async upsertBusinessProfile(
    userId: string,
    profileData: Omit<
      BusinessProfile,
      'id' | 'userId' | 'createdAt' | 'updatedAt'
    >
  ): Promise<ServiceResponse<BusinessProfile>> {
    try {
      // Step 1: Always fetch first to check if profile exists
      const { data: existingData, error: fetchError } = await supabase
        .from('business_profiles')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (fetchError && fetchError.code !== 'PGRST116') {
        // PGRST116 is "No rows returned" - this is expected when profile doesn't exist
        // Any other error is unexpected and should be returned
        console.error('Error fetching existing business profile:', fetchError);
        return {
          data: null,
          error:
            fetchError.message || 'Failed to check existing business profile',
        };
      }

      const profileExists = !fetchError && existingData;
      const now = new Date().toISOString();

      // Step 2: Prepare the data to be upserted
      const profileDataToUpsert = {
        business_name: profileData.businessName,
        business_address: profileData.businessAddress,
        business_type: profileData.businessType,
        point_value_percentage: profileData.pointValuePercentage.toString(),
        minimum_spend_for_redemption:
          profileData.minimumSpendForRedemption.toString(),
        onboarding_completed: profileData.onboardingCompleted,
        onboarding_step: profileData.onboardingStep,
        updated_at: now,
      };

      let result;

      if (profileExists) {
        // Step 3a: Update existing profile
        console.log('Updating existing business profile for user:', userId);

        const { data, error } = await supabase
          .from('business_profiles')
          .update(profileDataToUpsert)
          .eq('user_id', userId)
          .select()
          .single();

        if (error) {
          console.error('Error updating business profile:', error);
          return {
            data: null,
            error: error.message || 'Failed to update business profile',
          };
        }

        result = data;
      } else {
        // Step 3b: Create new profile
        console.log('Creating new business profile for user:', userId);

        const { data, error } = await supabase
          .from('business_profiles')
          .insert({
            user_id: userId,
            ...profileDataToUpsert,
            created_at: now,
          })
          .select()
          .single();

        if (error) {
          console.error('Error creating business profile:', error);
          return {
            data: null,
            error: error.message || 'Failed to create business profile',
          };
        }

        result = data;
      }

      // Step 4: Return the formatted result
      return {
        data: {
          id: result.id,
          userId: result.user_id,
          businessName: result.business_name,
          businessAddress: result.business_address,
          businessType: result.business_type,
          pointValuePercentage: parseFloat(
            result.point_value_percentage || '0'
          ),
          minimumSpendForRedemption: parseFloat(
            result.minimum_spend_for_redemption || '0'
          ),
          onboardingCompleted: result.onboarding_completed || false,
          onboardingStep: result.onboarding_step || 0,
          createdAt: result.created_at,
          updatedAt: result.updated_at,
        },
        error: null,
      };
    } catch (error) {
      console.error('Unexpected error in upsertBusinessProfile:', error);
      return {
        data: null,
        error:
          error instanceof Error
            ? error.message
            : 'An unexpected error occurred during upsert operation',
      };
    }
  }

  /**
   * Get business points summary
   * @param businessId - The business's UUID
   * @returns Promise with business points summary data or error
   */
  static async getBusinessPointsSummary(
    businessId: string
  ): Promise<ServiceResponse<BusinessPointsSummary>> {
    try {
      const { data, error } = await supabase.rpc(
        'get_business_points_summary',
        {
          p_business_id: businessId,
        }
      );

      if (error) {
        console.error('Error fetching business points summary:', error);
        return {
          data: null,
          error: error.message || 'Failed to fetch business points summary',
        };
      }

      // The function returns an array with a single object
      const summary = data?.[0];

      if (!summary) {
        return {
          data: {
            totalEarned: 0,
            totalActive: 0,
            totalRedeemed: 0,
          },
          error: null,
        };
      }

      return {
        data: {
          totalEarned: summary.total_earned || 0,
          totalActive: summary.total_active || 0,
          totalRedeemed: summary.total_redeemed || 0,
        },
        error: null,
      };
    } catch (error) {
      console.error('Unexpected error in getBusinessPointsSummary:', error);
      return {
        data: null,
        error:
          error instanceof Error
            ? error.message
            : 'An unexpected error occurred',
      };
    }
  }

  /**
   * Get business customer summaries
   * @param businessId - The business's UUID
   * @returns Promise with business customer summaries data or error
   */
  static async getBusinessCustomerSummaries(
    businessId: string
  ): Promise<ServiceResponse<BusinessCustomerSummary[]>> {
    try {
      const { data, error } = await supabase.rpc(
        'get_business_customer_summaries',
        {
          p_business_id: businessId,
        }
      );

      if (error) {
        console.error('Error fetching business customer summaries:', error);
        return {
          data: null,
          error: error.message || 'Failed to fetch business customer summaries',
        };
      }

      if (!data || !Array.isArray(data)) {
        return {
          data: [],
          error: null,
        };
      }

      // Map the database response to our expected format
      const customers: BusinessCustomerSummary[] = data.map((item: any) => ({
        customerId: item.customer_id,
        customerDisplayName: item.customer_display_name,
        activePoints: item.active_points || 0,
        earnedPoints: item.earned_points || 0,
        redeemedPoints: item.redeemed_points || 0,
        lastTransactionDate: item.last_transaction_date,
        totalTransactions: item.total_transactions || 0,
        totalAmountSpent: parseFloat(item.total_amount_spent || '0'),
      }));

      return {
        data: customers,
        error: null,
      };
    } catch (error) {
      console.error('Unexpected error in getBusinessCustomerSummaries:', error);
      return {
        data: null,
        error:
          error instanceof Error
            ? error.message
            : 'An unexpected error occurred',
      };
    }
  }

  /**
   * Get business transaction history
   * @param businessId - The business's UUID
   * @param page - Page number (default: 1)
   * @param pageSize - Number of transactions per page (default: 100)
   * @returns Promise with business transaction history data or error
   */
  static async getBusinessTransactionHistory(
    businessId: string,
    page: number = 1,
    pageSize: number = 100
  ): Promise<ServiceResponse<BusinessTransaction[]>> {
    try {
      const { data, error } = await supabase
        .from('transactions')
        .select('*')
        .eq('business_id', businessId)
        .order('created_at', { ascending: false })
        .range((page - 1) * pageSize, page * pageSize - 1);

      if (error) {
        console.error('Error fetching business transaction history:', error);
        return {
          data: null,
          error:
            error.message || 'Failed to fetch business transaction history',
        };
      }

      if (!data || !Array.isArray(data)) {
        return {
          data: [],
          error: null,
        };
      }

      // Map the database response to our expected format
      const transactions: BusinessTransaction[] = data.map((item: any) => ({
        id: item.id,
        customerId: item.customer_id,
        businessId: item.business_id,
        amountSpent: parseFloat(item.amount_spent || '0'),
        pointsAwarded: item.points_awarded || 0,
        pointsRedeemed: item.points_redeemed || 0,
        qrToken: item.qr_token,
        type: item.type?.toLowerCase() || 'purchase',
        createdAt: item.created_at,
        updatedAt: item.updated_at,
      }));

      return {
        data: transactions,
        error: null,
      };
    } catch (error) {
      console.error(
        'Unexpected error in getBusinessTransactionHistory:',
        error
      );
      return {
        data: null,
        error:
          error instanceof Error
            ? error.message
            : 'An unexpected error occurred',
      };
    }
  }

  /**
   * Create a purchase transaction with points
   * @param transactionData - The purchase transaction data
   * @returns Promise with created transaction data or error
   */
  static async createPurchaseTransaction(
    transactionData: PurchaseTransactionRequest
  ): Promise<ServiceResponse<BusinessTransaction>> {
    try {
      // Calculate points: 1 point per £1 spent, always rounded up
      const pointsAwarded = Math.ceil(transactionData.amountSpent);

      const { data, error } = await supabase.rpc(
        'create_transaction_with_points',
        {
          p_customer_id: transactionData.customerId,
          p_business_id: transactionData.businessId,
          p_amount_spent: transactionData.amountSpent,
          p_points_awarded: pointsAwarded,
          p_qr_token: transactionData.qrToken,
          p_type: 'PURCHASE',
        }
      );

      if (error) {
        console.error('Error creating purchase transaction:', error);
        return {
          data: null,
          error: error.message || 'Failed to create purchase transaction',
        };
      }

      // The function returns an array with a single object
      const transaction = data?.[0];

      if (!transaction) {
        return {
          data: null,
          error: 'No transaction data returned',
        };
      }

      return {
        data: {
          id: transaction.id,
          customerId: transaction.customer_id,
          businessId: transaction.business_id,
          amountSpent: parseFloat(transaction.amount_spent || '0'),
          pointsAwarded: transaction.points_awarded || 0,
          pointsRedeemed: transaction.points_redeemed || 0,
          qrToken: transaction.qr_token,
          type: transaction.type?.toLowerCase() || 'purchase',
          createdAt: transaction.created_at,
          updatedAt: transaction.updated_at,
        },
        error: null,
      };
    } catch (error) {
      console.error('Unexpected error in createPurchaseTransaction:', error);
      return {
        data: null,
        error:
          error instanceof Error
            ? error.message
            : 'An unexpected error occurred',
      };
    }
  }
}
