import { BusinessService } from '../businessService';
import { supabase } from '@indie-points/lib';
import { PurchaseTransactionRequest } from '../types';

// Mock the supabase client
jest.mock('@indie-points/lib', () => ({
  supabase: {
    from: jest.fn(),
    rpc: jest.fn(),
  },
}));

const mockSupabase = supabase as jest.Mocked<typeof supabase>;

describe('BusinessService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getBusinessProfile', () => {
    const mockUserId = 'test-user-id';

    it('should return business profile when successful', async () => {
      const mockProfileData = {
        id: 'profile-id',
        user_id: mockUserId,
        business_name: 'Test Business',
        business_address: '123 Test St',
        business_type: 'restaurant',
        point_value_percentage: '5.0',
        minimum_spend_for_redemption: '10.0',
        onboarding_completed: true,
        onboarding_step: 3,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-02T00:00:00Z',
      };

      const mockSelect = jest.fn().mockReturnValue({
        eq: jest.fn().mockReturnValue({
          single: jest.fn().mockResolvedValue({
            data: mockProfileData,
            error: null,
          }),
        }),
      });

      mockSupabase.from.mockReturnValue({
        select: mockSelect,
      } as any);

      const result = await BusinessService.getBusinessProfile(mockUserId);

      expect(result.data).toEqual({
        id: 'profile-id',
        userId: mockUserId,
        businessName: 'Test Business',
        businessAddress: '123 Test St',
        businessType: 'restaurant',
        pointValuePercentage: 5.0,
        minimumSpendForRedemption: 10.0,
        onboardingCompleted: true,
        onboardingStep: 3,
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-02T00:00:00Z',
      });
      expect(result.error).toBeNull();
    });

    it('should return null data when business profile does not exist', async () => {
      const mockSelect = jest.fn().mockReturnValue({
        eq: jest.fn().mockReturnValue({
          single: jest.fn().mockResolvedValue({
            data: null,
            error: { code: 'PGRST116', message: 'No rows returned' },
          }),
        }),
      });

      mockSupabase.from.mockReturnValue({
        select: mockSelect,
      } as any);

      const result = await BusinessService.getBusinessProfile(mockUserId);

      expect(result.data).toBeNull();
      expect(result.error).toBeNull();
    });

    it('should return error when database query fails', async () => {
      const mockSelect = jest.fn().mockReturnValue({
        eq: jest.fn().mockReturnValue({
          single: jest.fn().mockResolvedValue({
            data: null,
            error: { message: 'Database connection failed' },
          }),
        }),
      });

      mockSupabase.from.mockReturnValue({
        select: mockSelect,
      } as any);

      const result = await BusinessService.getBusinessProfile(mockUserId);

      expect(result.data).toBeNull();
      expect(result.error).toBe('Database connection failed');
    });

    it('should handle unexpected errors', async () => {
      const mockSelect = jest.fn().mockReturnValue({
        eq: jest.fn().mockReturnValue({
          single: jest.fn().mockRejectedValue(new Error('Network error')),
        }),
      });

      mockSupabase.from.mockReturnValue({
        select: mockSelect,
      } as any);

      const result = await BusinessService.getBusinessProfile(mockUserId);

      expect(result.data).toBeNull();
      expect(result.error).toBe('Network error');
    });
  });

  describe('upsertBusinessProfile', () => {
    const mockUserId = 'test-user-id';
    const mockProfileData = {
      businessName: 'Test Business',
      businessAddress: '123 Test St',
      businessType: 'restaurant',
      pointValuePercentage: 5.0,
      minimumSpendForRedemption: 10.0,
      onboardingCompleted: true,
      onboardingStep: 3,
    };

    it('should create new business profile when one does not exist', async () => {
      // Mock fetch existing profile - returns no rows
      const mockFetchSelect = jest.fn().mockReturnValue({
        eq: jest.fn().mockReturnValue({
          single: jest.fn().mockResolvedValue({
            data: null,
            error: { code: 'PGRST116', message: 'No rows returned' },
          }),
        }),
      });

      // Mock insert new profile
      const mockInsert = jest.fn().mockReturnValue({
        select: jest.fn().mockReturnValue({
          single: jest.fn().mockResolvedValue({
            data: {
              id: 'new-profile-id',
              user_id: mockUserId,
              business_name: 'Test Business',
              business_address: '123 Test St',
              business_type: 'restaurant',
              point_value_percentage: '5.0',
              minimum_spend_for_redemption: '10.0',
              onboarding_completed: true,
              onboarding_step: 3,
              created_at: '2024-01-01T00:00:00Z',
              updated_at: '2024-01-01T00:00:00Z',
            },
            error: null,
          }),
        }),
      });

      mockSupabase.from.mockReturnValueOnce({
        select: mockFetchSelect,
      } as any);
      mockSupabase.from.mockReturnValueOnce({
        insert: mockInsert,
      } as any);

      const result = await BusinessService.upsertBusinessProfile(
        mockUserId,
        mockProfileData
      );

      expect(result.data).toEqual({
        id: 'new-profile-id',
        userId: mockUserId,
        businessName: 'Test Business',
        businessAddress: '123 Test St',
        businessType: 'restaurant',
        pointValuePercentage: 5.0,
        minimumSpendForRedemption: 10.0,
        onboardingCompleted: true,
        onboardingStep: 3,
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
      });
      expect(result.error).toBeNull();
    });

    it('should update existing business profile when one exists', async () => {
      // Mock fetch existing profile - returns existing data
      const mockFetchSelect = jest.fn().mockReturnValue({
        eq: jest.fn().mockReturnValue({
          single: jest.fn().mockResolvedValue({
            data: {
              id: 'existing-profile-id',
              user_id: mockUserId,
              business_name: 'Old Business Name',
              business_address: 'Old Address',
              business_type: 'cafe',
              point_value_percentage: '3.0',
              minimum_spend_for_redemption: '5.0',
              onboarding_completed: false,
              onboarding_step: 1,
              created_at: '2024-01-01T00:00:00Z',
              updated_at: '2024-01-01T00:00:00Z',
            },
            error: null,
          }),
        }),
      });

      // Mock update existing profile
      const mockUpdate = jest.fn().mockReturnValue({
        eq: jest.fn().mockReturnValue({
          select: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: {
                id: 'existing-profile-id',
                user_id: mockUserId,
                business_name: 'Test Business',
                business_address: '123 Test St',
                business_type: 'restaurant',
                point_value_percentage: '5.0',
                minimum_spend_for_redemption: '10.0',
                onboarding_completed: true,
                onboarding_step: 3,
                created_at: '2024-01-01T00:00:00Z',
                updated_at: '2024-01-02T00:00:00Z',
              },
              error: null,
            }),
          }),
        }),
      });

      mockSupabase.from.mockReturnValueOnce({
        select: mockFetchSelect,
      } as any);
      mockSupabase.from.mockReturnValueOnce({
        update: mockUpdate,
      } as any);

      const result = await BusinessService.upsertBusinessProfile(
        mockUserId,
        mockProfileData
      );

      expect(result.data).toEqual({
        id: 'existing-profile-id',
        userId: mockUserId,
        businessName: 'Test Business',
        businessAddress: '123 Test St',
        businessType: 'restaurant',
        pointValuePercentage: 5.0,
        minimumSpendForRedemption: 10.0,
        onboardingCompleted: true,
        onboardingStep: 3,
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-02T00:00:00Z',
      });
      expect(result.error).toBeNull();
    });

    it('should return error when fetch existing profile fails', async () => {
      const mockFetchSelect = jest.fn().mockReturnValue({
        eq: jest.fn().mockReturnValue({
          single: jest.fn().mockResolvedValue({
            data: null,
            error: { message: 'Database connection failed' },
          }),
        }),
      });

      mockSupabase.from.mockReturnValue({
        select: mockFetchSelect,
      } as any);

      const result = await BusinessService.upsertBusinessProfile(
        mockUserId,
        mockProfileData
      );

      expect(result.data).toBeNull();
      expect(result.error).toBe('Database connection failed');
    });

    it('should return error when insert fails', async () => {
      // Mock fetch existing profile - returns no rows
      const mockFetchSelect = jest.fn().mockReturnValue({
        eq: jest.fn().mockReturnValue({
          single: jest.fn().mockResolvedValue({
            data: null,
            error: { code: 'PGRST116', message: 'No rows returned' },
          }),
        }),
      });

      // Mock insert fails
      const mockInsert = jest.fn().mockReturnValue({
        select: jest.fn().mockReturnValue({
          single: jest.fn().mockResolvedValue({
            data: null,
            error: { message: 'Insert failed' },
          }),
        }),
      });

      mockSupabase.from.mockReturnValueOnce({
        select: mockFetchSelect,
      } as any);
      mockSupabase.from.mockReturnValueOnce({
        insert: mockInsert,
      } as any);

      const result = await BusinessService.upsertBusinessProfile(
        mockUserId,
        mockProfileData
      );

      expect(result.data).toBeNull();
      expect(result.error).toBe('Insert failed');
    });

    it('should return error when update fails', async () => {
      // Mock fetch existing profile - returns existing data
      const mockFetchSelect = jest.fn().mockReturnValue({
        eq: jest.fn().mockReturnValue({
          single: jest.fn().mockResolvedValue({
            data: { id: 'existing-id' },
            error: null,
          }),
        }),
      });

      // Mock update fails
      const mockUpdate = jest.fn().mockReturnValue({
        eq: jest.fn().mockReturnValue({
          select: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: null,
              error: { message: 'Update failed' },
            }),
          }),
        }),
      });

      mockSupabase.from.mockReturnValueOnce({
        select: mockFetchSelect,
      } as any);
      mockSupabase.from.mockReturnValueOnce({
        update: mockUpdate,
      } as any);

      const result = await BusinessService.upsertBusinessProfile(
        mockUserId,
        mockProfileData
      );

      expect(result.data).toBeNull();
      expect(result.error).toBe('Update failed');
    });
  });

  describe('getBusinessPointsSummary', () => {
    const mockBusinessId = 'test-business-id';

    it('should return business points summary when successful', async () => {
      const mockSummaryData = [
        {
          total_earned: 1000,
          total_active: 750,
          total_redeemed: 250,
        },
      ];

      mockSupabase.rpc.mockResolvedValue({
        data: mockSummaryData,
        error: null,
        count: null,
        status: 200,
        statusText: 'OK',
      });

      const result =
        await BusinessService.getBusinessPointsSummary(mockBusinessId);

      expect(result.data).toEqual({
        totalEarned: 1000,
        totalActive: 750,
        totalRedeemed: 250,
      });
      expect(result.error).toBeNull();
      expect(mockSupabase.rpc).toHaveBeenCalledWith(
        'get_business_points_summary',
        {
          p_business_id: mockBusinessId,
        }
      );
    });

    it('should return default values when no summary data exists', async () => {
      mockSupabase.rpc.mockResolvedValue({
        data: [],
        error: null,
        count: null,
        status: 200,
        statusText: 'OK',
      });

      const result =
        await BusinessService.getBusinessPointsSummary(mockBusinessId);

      expect(result.data).toEqual({
        totalEarned: 0,
        totalActive: 0,
        totalRedeemed: 0,
      });
      expect(result.error).toBeNull();
    });

    it('should return default values when summary data is null', async () => {
      mockSupabase.rpc.mockResolvedValue({
        data: [null],
        error: null,
        count: null,
        status: 200,
        statusText: 'OK',
      });

      const result =
        await BusinessService.getBusinessPointsSummary(mockBusinessId);

      expect(result.data).toEqual({
        totalEarned: 0,
        totalActive: 0,
        totalRedeemed: 0,
      });
      expect(result.error).toBeNull();
    });

    it('should return error when database query fails', async () => {
      mockSupabase.rpc.mockResolvedValue({
        data: null,
        error: {
          message: 'Database connection failed',
          details: 'Connection refused',
          hint: 'Check your database connection',
          code: '500',
          name: 'DatabaseError',
        },
        count: null,
        status: 500,
        statusText: 'Internal Server Error',
      });

      const result =
        await BusinessService.getBusinessPointsSummary(mockBusinessId);

      expect(result.data).toBeNull();
      expect(result.error).toBe('Database connection failed');
    });

    it('should handle unexpected errors', async () => {
      mockSupabase.rpc.mockRejectedValue(new Error('Network error'));

      const result =
        await BusinessService.getBusinessPointsSummary(mockBusinessId);

      expect(result.data).toBeNull();
      expect(result.error).toBe('Network error');
    });
  });

  describe('getBusinessCustomerSummaries', () => {
    const mockBusinessId = 'test-business-id';

    it('should return customer summaries when successful', async () => {
      const mockCustomerData = [
        {
          customer_id: 'customer-1',
          customer_display_name: 'John Doe',
          active_points: 100,
          earned_points: 500,
          redeemed_points: 400,
          last_transaction_date: '2024-01-15T00:00:00Z',
          total_transactions: 10,
          total_amount_spent: '250.50',
        },
        {
          customer_id: 'customer-2',
          customer_display_name: 'Jane Smith',
          active_points: 75,
          earned_points: 300,
          redeemed_points: 225,
          last_transaction_date: '2024-01-10T00:00:00Z',
          total_transactions: 5,
          total_amount_spent: '150.25',
        },
      ];

      mockSupabase.rpc.mockResolvedValue({
        data: mockCustomerData,
        error: null,
        count: null,
        status: 200,
        statusText: 'OK',
      });

      const result =
        await BusinessService.getBusinessCustomerSummaries(mockBusinessId);

      expect(result.data).toEqual([
        {
          customerId: 'customer-1',
          customerDisplayName: 'John Doe',
          activePoints: 100,
          earnedPoints: 500,
          redeemedPoints: 400,
          lastTransactionDate: '2024-01-15T00:00:00Z',
          totalTransactions: 10,
          totalAmountSpent: 250.5,
        },
        {
          customerId: 'customer-2',
          customerDisplayName: 'Jane Smith',
          activePoints: 75,
          earnedPoints: 300,
          redeemedPoints: 225,
          lastTransactionDate: '2024-01-10T00:00:00Z',
          totalTransactions: 5,
          totalAmountSpent: 150.25,
        },
      ]);
      expect(result.error).toBeNull();
      expect(mockSupabase.rpc).toHaveBeenCalledWith(
        'get_business_customer_summaries',
        {
          p_business_id: mockBusinessId,
        }
      );
    });

    it('should return empty array when no customer data exists', async () => {
      mockSupabase.rpc.mockResolvedValue({
        data: [],
        error: null,
        count: null,
        status: 200,
        statusText: 'OK',
      });

      const result =
        await BusinessService.getBusinessCustomerSummaries(mockBusinessId);

      expect(result.data).toEqual([]);
      expect(result.error).toBeNull();
    });

    it('should return empty array when customer data is null', async () => {
      mockSupabase.rpc.mockResolvedValue({
        data: null,
        error: null,
        count: null,
        status: 200,
        statusText: 'OK',
      });

      const result =
        await BusinessService.getBusinessCustomerSummaries(mockBusinessId);

      expect(result.data).toEqual([]);
      expect(result.error).toBeNull();
    });

    it('should return error when database query fails', async () => {
      mockSupabase.rpc.mockResolvedValue({
        data: null,
        error: {
          message: 'Database connection failed',
          details: 'Connection refused',
          hint: 'Check your database connection',
          code: '500',
          name: 'DatabaseError',
        },
        count: null,
        status: 500,
        statusText: 'Internal Server Error',
      });

      const result =
        await BusinessService.getBusinessCustomerSummaries(mockBusinessId);

      expect(result.data).toBeNull();
      expect(result.error).toBe('Database connection failed');
    });

    it('should handle unexpected errors', async () => {
      mockSupabase.rpc.mockRejectedValue(new Error('Network error'));

      const result =
        await BusinessService.getBusinessCustomerSummaries(mockBusinessId);

      expect(result.data).toBeNull();
      expect(result.error).toBe('Network error');
    });
  });

  describe('getBusinessTransactionHistory', () => {
    const mockBusinessId = 'test-business-id';

    it('should return transaction history when successful', async () => {
      const mockTransactionData = [
        {
          id: 'transaction-1',
          customer_id: 'customer-1',
          business_id: mockBusinessId,
          amount_spent: '50.00',
          points_awarded: 25,
          points_redeemed: 0,
          qr_token: 'qr-token-1',
          type: 'PURCHASE',
          created_at: '2024-01-15T00:00:00Z',
          updated_at: '2024-01-15T00:00:00Z',
        },
        {
          id: 'transaction-2',
          customer_id: 'customer-2',
          business_id: mockBusinessId,
          amount_spent: '30.00',
          points_awarded: 15,
          points_redeemed: 0,
          qr_token: 'qr-token-2',
          type: 'PURCHASE',
          created_at: '2024-01-14T00:00:00Z',
          updated_at: '2024-01-14T00:00:00Z',
        },
      ];

      const mockSelect = jest.fn().mockReturnValue({
        eq: jest.fn().mockReturnValue({
          order: jest.fn().mockReturnValue({
            range: jest.fn().mockResolvedValue({
              data: mockTransactionData,
              error: null,
            }),
          }),
        }),
      });

      mockSupabase.from.mockReturnValue({
        select: mockSelect,
      } as any);

      const result =
        await BusinessService.getBusinessTransactionHistory(mockBusinessId);

      expect(result.data).toEqual([
        {
          id: 'transaction-1',
          customerId: 'customer-1',
          businessId: mockBusinessId,
          amountSpent: 50.0,
          pointsAwarded: 25,
          pointsRedeemed: 0,
          qrToken: 'qr-token-1',
          type: 'purchase',
          createdAt: '2024-01-15T00:00:00Z',
          updatedAt: '2024-01-15T00:00:00Z',
        },
        {
          id: 'transaction-2',
          customerId: 'customer-2',
          businessId: mockBusinessId,
          amountSpent: 30.0,
          pointsAwarded: 15,
          pointsRedeemed: 0,
          qrToken: 'qr-token-2',
          type: 'purchase',
          createdAt: '2024-01-14T00:00:00Z',
          updatedAt: '2024-01-14T00:00:00Z',
        },
      ]);
      expect(result.error).toBeNull();
    });

    it('should return empty array when no transactions exist', async () => {
      const mockSelect = jest.fn().mockReturnValue({
        eq: jest.fn().mockReturnValue({
          order: jest.fn().mockReturnValue({
            range: jest.fn().mockResolvedValue({
              data: [],
              error: null,
            }),
          }),
        }),
      });

      mockSupabase.from.mockReturnValue({
        select: mockSelect,
      } as any);

      const result =
        await BusinessService.getBusinessTransactionHistory(mockBusinessId);

      expect(result.data).toEqual([]);
      expect(result.error).toBeNull();
    });

    it('should return error when database query fails', async () => {
      const mockSelect = jest.fn().mockReturnValue({
        eq: jest.fn().mockReturnValue({
          order: jest.fn().mockReturnValue({
            range: jest.fn().mockResolvedValue({
              data: null,
              error: { message: 'Database connection failed' },
            }),
          }),
        }),
      });

      mockSupabase.from.mockReturnValue({
        select: mockSelect,
      } as any);

      const result =
        await BusinessService.getBusinessTransactionHistory(mockBusinessId);

      expect(result.data).toBeNull();
      expect(result.error).toBe('Database connection failed');
    });

    it('should handle pagination parameters', async () => {
      const mockSelect = jest.fn().mockReturnValue({
        eq: jest.fn().mockReturnValue({
          order: jest.fn().mockReturnValue({
            range: jest.fn().mockResolvedValue({
              data: [],
              error: null,
            }),
          }),
        }),
      });

      mockSupabase.from.mockReturnValue({
        select: mockSelect,
      } as any);

      await BusinessService.getBusinessTransactionHistory(
        mockBusinessId,
        2,
        50
      );

      expect(mockSelect).toHaveBeenCalledWith('*');
      expect(mockSelect().eq).toHaveBeenCalledWith(
        'business_id',
        mockBusinessId
      );
      expect(mockSelect().eq().order).toHaveBeenCalledWith('created_at', {
        ascending: false,
      });
      expect(mockSelect().eq().order().range).toHaveBeenCalledWith(50, 99);
    });

    it('should handle unexpected errors', async () => {
      const mockSelect = jest.fn().mockReturnValue({
        eq: jest.fn().mockReturnValue({
          order: jest.fn().mockReturnValue({
            range: jest.fn().mockRejectedValue(new Error('Network error')),
          }),
        }),
      });

      mockSupabase.from.mockReturnValue({
        select: mockSelect,
      } as any);

      const result =
        await BusinessService.getBusinessTransactionHistory(mockBusinessId);

      expect(result.data).toBeNull();
      expect(result.error).toBe('Network error');
    });
  });

  describe('createPurchaseTransaction', () => {
    const mockTransactionData: PurchaseTransactionRequest = {
      customerId: 'customer-1',
      businessId: 'business-1',
      amountSpent: 50.0,
      qrToken: 'qr-token-1',
    };

    it('should create purchase transaction when successful', async () => {
      const mockTransactionResult = [
        {
          id: 'transaction-1',
          customer_id: 'customer-1',
          business_id: 'business-1',
          amount_spent: '50.00',
          points_awarded: 50,
          points_redeemed: 0,
          qr_token: 'qr-token-1',
          type: 'PURCHASE',
          created_at: '2024-01-15T00:00:00Z',
          updated_at: '2024-01-15T00:00:00Z',
        },
      ];

      mockSupabase.rpc.mockResolvedValue({
        data: mockTransactionResult,
        error: null,
        count: null,
        status: 200,
        statusText: 'OK',
      });

      const result =
        await BusinessService.createPurchaseTransaction(mockTransactionData);

      expect(result.data).toEqual({
        id: 'transaction-1',
        customerId: 'customer-1',
        businessId: 'business-1',
        amountSpent: 50.0,
        pointsAwarded: 50,
        pointsRedeemed: 0,
        qrToken: 'qr-token-1',
        type: 'purchase',
        createdAt: '2024-01-15T00:00:00Z',
        updatedAt: '2024-01-15T00:00:00Z',
      });
      expect(result.error).toBeNull();
      expect(mockSupabase.rpc).toHaveBeenCalledWith(
        'create_transaction_with_points',
        {
          p_customer_id: 'customer-1',
          p_business_id: 'business-1',
          p_amount_spent: 50.0,
          p_points_awarded: 50,
          p_qr_token: 'qr-token-1',
          p_type: 'PURCHASE',
        }
      );
    });

    it('should return error when no transaction data is returned', async () => {
      mockSupabase.rpc.mockResolvedValue({
        data: [],
        error: null,
        count: null,
        status: 200,
        statusText: 'OK',
      });

      const result =
        await BusinessService.createPurchaseTransaction(mockTransactionData);

      expect(result.data).toBeNull();
      expect(result.error).toBe('No transaction data returned');
    });

    it('should return error when database query fails', async () => {
      mockSupabase.rpc.mockResolvedValue({
        data: null,
        error: {
          message: 'Database connection failed',
          details: 'Connection refused',
          hint: 'Check your database connection',
          code: '500',
          name: 'DatabaseError',
        },
        count: null,
        status: 500,
        statusText: 'Internal Server Error',
      });

      const result =
        await BusinessService.createPurchaseTransaction(mockTransactionData);

      expect(result.data).toBeNull();
      expect(result.error).toBe('Database connection failed');
    });

    it('should handle unexpected errors', async () => {
      mockSupabase.rpc.mockRejectedValue(new Error('Network error'));

      const result =
        await BusinessService.createPurchaseTransaction(mockTransactionData);

      expect(result.data).toBeNull();
      expect(result.error).toBe('Network error');
    });

    it('should calculate points correctly for whole pound amounts', async () => {
      const transactionData = {
        customerId: 'customer-1',
        businessId: 'business-1',
        amountSpent: 25.0,
        qrToken: 'qr-token-1',
      };

      const mockTransactionResult = [
        {
          id: 'transaction-1',
          customer_id: 'customer-1',
          business_id: 'business-1',
          amount_spent: '25.00',
          points_awarded: 25,
          points_redeemed: 0,
          qr_token: 'qr-token-1',
          type: 'PURCHASE',
          created_at: '2024-01-15T00:00:00Z',
          updated_at: '2024-01-15T00:00:00Z',
        },
      ];

      mockSupabase.rpc.mockResolvedValue({
        data: mockTransactionResult,
        error: null,
        count: null,
        status: 200,
        statusText: 'OK',
      });

      await BusinessService.createPurchaseTransaction(transactionData);

      expect(mockSupabase.rpc).toHaveBeenCalledWith(
        'create_transaction_with_points',
        {
          p_customer_id: 'customer-1',
          p_business_id: 'business-1',
          p_amount_spent: 25.0,
          p_points_awarded: 25,
          p_qr_token: 'qr-token-1',
          p_type: 'PURCHASE',
        }
      );
    });

    it('should round up points for decimal amounts', async () => {
      const transactionData = {
        customerId: 'customer-1',
        businessId: 'business-1',
        amountSpent: 25.50,
        qrToken: 'qr-token-1',
      };

      const mockTransactionResult = [
        {
          id: 'transaction-1',
          customer_id: 'customer-1',
          business_id: 'business-1',
          amount_spent: '25.50',
          points_awarded: 26,
          points_redeemed: 0,
          qr_token: 'qr-token-1',
          type: 'PURCHASE',
          created_at: '2024-01-15T00:00:00Z',
          updated_at: '2024-01-15T00:00:00Z',
        },
      ];

      mockSupabase.rpc.mockResolvedValue({
        data: mockTransactionResult,
        error: null,
        count: null,
        status: 200,
        statusText: 'OK',
      });

      await BusinessService.createPurchaseTransaction(transactionData);

      expect(mockSupabase.rpc).toHaveBeenCalledWith(
        'create_transaction_with_points',
        {
          p_customer_id: 'customer-1',
          p_business_id: 'business-1',
          p_amount_spent: 25.50,
          p_points_awarded: 26,
          p_qr_token: 'qr-token-1',
          p_type: 'PURCHASE',
        }
      );
    });

    it('should round up points for small decimal amounts', async () => {
      const transactionData = {
        customerId: 'customer-1',
        businessId: 'business-1',
        amountSpent: 0.01,
        qrToken: 'qr-token-1',
      };

      const mockTransactionResult = [
        {
          id: 'transaction-1',
          customer_id: 'customer-1',
          business_id: 'business-1',
          amount_spent: '0.01',
          points_awarded: 1,
          points_redeemed: 0,
          qr_token: 'qr-token-1',
          type: 'PURCHASE',
          created_at: '2024-01-15T00:00:00Z',
          updated_at: '2024-01-15T00:00:00Z',
        },
      ];

      mockSupabase.rpc.mockResolvedValue({
        data: mockTransactionResult,
        error: null,
        count: null,
        status: 200,
        statusText: 'OK',
      });

      await BusinessService.createPurchaseTransaction(transactionData);

      expect(mockSupabase.rpc).toHaveBeenCalledWith(
        'create_transaction_with_points',
        {
          p_customer_id: 'customer-1',
          p_business_id: 'business-1',
          p_amount_spent: 0.01,
          p_points_awarded: 1,
          p_qr_token: 'qr-token-1',
          p_type: 'PURCHASE',
        }
      );
    });
  });
});
