import React from 'react';
import { VStack } from '@indie-points/ui-vstack';
import { HStack } from '@indie-points/ui-hstack';
import { Button, ButtonText } from '@indie-points/ui-button';
import { Text } from '@indie-points/ui-text';
import * as Haptics from 'expo-haptics';

interface SocialButtonProps {
  onPress: () => void;
  children: React.ReactNode;
  variant?: 'outline' | 'solid';
}

const SocialButton = ({
  onPress,
  children,
  variant = 'outline',
}: SocialButtonProps) => (
  <Button
    variant={variant}
    size='lg'
    className='w-full bg-background-0 border-2 border-outline-300 rounded-xl shadow-sm'
    onPress={async () => {
      await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      onPress();
    }}
  >
    <HStack space='sm' className='items-center'>
      {children}
    </HStack>
  </Button>
);

interface SocialLoginButtonsProps {
  onSocialLogin: (provider: 'google' | 'facebook' | 'twitter') => void;
}

export function SocialLoginButtons({ onSocialLogin }: SocialLoginButtonsProps) {
  return (
    <VStack space='md'>
      <SocialButton onPress={() => onSocialLogin('google')}>
        <Text className='text-2xl'>🔍</Text>
        <ButtonText className='text-typography-900 font-medium'>
          Continue with Google
        </ButtonText>
      </SocialButton>

      <SocialButton onPress={() => onSocialLogin('facebook')}>
        <Text className='text-2xl'>📘</Text>
        <ButtonText className='text-typography-900 font-medium'>
          Continue with Facebook
        </ButtonText>
      </SocialButton>

      <SocialButton onPress={() => onSocialLogin('twitter')}>
        <Text className='text-2xl'>❌</Text>
        <ButtonText className='text-typography-900 font-medium'>
          Continue with Twitter
        </ButtonText>
      </SocialButton>
    </VStack>
  );
}
